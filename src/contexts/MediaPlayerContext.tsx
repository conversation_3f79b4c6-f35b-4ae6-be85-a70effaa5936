import React, { createContext, useContext, useState, ReactNode } from 'react';

interface MediaPlayerContextType {
  playerHeight: number;
  setPlayerHeight: (height: number) => void;
}

const MediaPlayerContext = createContext<MediaPlayerContextType | undefined>(
  undefined
);

interface MediaPlayerProviderProps {
  children: ReactNode;
}

export const MediaPlayerProvider: React.FC<MediaPlayerProviderProps> = ({
  children,
}) => {
  const [playerHeight, setPlayerHeight] = useState(95); // 默认估算高度

  return (
    <MediaPlayerContext.Provider value={{ playerHeight, setPlayerHeight }}>
      {children}
    </MediaPlayerContext.Provider>
  );
};

export const useMediaPlayerContext = () => {
  const context = useContext(MediaPlayerContext);
  if (context === undefined) {
    // 提供一个fallback，避免在没有Provider时崩溃
    console.warn(
      'useMediaPlayerContext used outside of MediaPlayerProvider, using default values'
    );
    return {
      playerHeight: 95, // 默认估算高度
      setPlayerHeight: () => {}, // 空函数
    };
  }
  return context;
};
