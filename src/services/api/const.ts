export type OrchestrationServiceCode =
  | 'checkUserInfo'
  | 'deleteRecordingRecord'
  | 'editRecordingRecordName'
  | 'addRecordingToKnowledgeGraph'
  | 'addNewRecording'
  | 'queryRecordingInfo'
  | 'addAudioTranscription'
  | 'updateTranscriptionProgress'
  | 'transcribeAudioToText'
  | 'batchDeleteRecordings'
  | 'queryRecordingRecord'
  | 'updateSpeakerName'
  | 'addRecordMark'
  | 'addScene'
  | 'deleteScene'
  | 'addSpaceFile'
  | 'changeSpaceScene'
  | 'deleteSpaceFile';

const orchestrationServiceList: Record<OrchestrationServiceCode, string> = {
  checkUserInfo: '1255823868596408320',
  deleteRecordingRecord: '1256801982608658432',
  editRecordingRecordName: '1256809468409499651',
  addRecordingToKnowledgeGraph: '1265643641963524096',
  addNewRecording: '1254369495106277382',
  queryRecordingInfo: '1265586453404430338',
  addAudioTranscription: '1265583359421558784',
  updateTranscriptionProgress: '1265865106205945859',
  transcribeAudioToText: '1265860408187006978',
  batchDeleteRecordings: '1265940622564896771',
  queryRecordingRecord: '1267091295608221698',
  updateSpeakerName: '1265956271198547968',
  addRecordMark: '1265965247126548482',
  addScene: '1255060052375851008',
  deleteScene: '1255061771872718851',
  addSpaceFile: '1255789327898988552',
  changeSpaceScene: '1255505497992548354',
  deleteSpaceFile: '1257532494549700608',
};

interface OrchestrationService {
  serviceCode: OrchestrationServiceCode;
  serviceVersionId: string;
}

const getOrchestrationByCode = (
  serviceCode: OrchestrationServiceCode
): OrchestrationService => ({
  serviceCode: serviceCode,
  serviceVersionId: orchestrationServiceList[serviceCode],
});

// 会话服务配置
export const SESSION_CONFIG = {
  TENANT_ID: '1265133818765635584',
  BOT_ID: '1266014058983452672', // 保持字符串格式避免精度丢失
  BOT_TENANT_ID: '1265133818765635584',
  BASE_URL: 'https://www.hjlingxi.com/BOTE/api/bote',
} as const;

// 聊天服务配置
export const CHAT_CONFIG = {
  // 默认场景配置
  DEFAULT_SCENE_ID: undefined, // 普通对话不设置sceneId

  // 功能开关默认值
  DEFAULT_FLAGS: {
    commonFlag: false,
    webSearchEnabled: false,
    deepThinkEnabled: false,
  },

  // 消息类型配置
  MESSAGE_TYPES: {
    USER_INPUT: 'input',
    SYSTEM_POINT: 'point',
    TEXT_RESPONSE: 'text',
  },

  // 消息角色
  MESSAGE_ROLES: {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system',
  },
} as const;

export { orchestrationServiceList, getOrchestrationByCode };
