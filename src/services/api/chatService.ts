import { apiClient } from './client';
import { SESSION_CONFIG, CHAT_CONFIG } from './const';
import EventSource from 'react-native-sse';
import AsyncStorage from '@react-native-async-storage/async-storage';

// React Native 兼容的 UUID 生成函数
const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// 聊天消息接口定义
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  type?: 'input' | 'point' | 'text';
  files?: Array<{
    fileId: string;
    fileName: string;
    fileType: string;
  }>;
}

export interface ChatCompletionsParams {
  botId: string;
  sessionId: string;
  message: ChatMessage;
  tenantId: string;
  commonFlag?: boolean;
  sceneId?: string;
  clientId: string;
  webSearchEnabled?: boolean;
  deepThinkEnabled?: boolean;
  params?: {
    knowledgeId?: string | null;
  };
}

export interface ChatCompletionsResponse {
  resultCode: string;
  resultMsg: string;
  resultObject?: any;
  stack?: string;
  errorInfos?: any[];
}

/**
 * 构建聊天请求参数
 * 基于 createSession 返回值和用户输入构建完整的请求参数
 */
export const buildChatParams = (
  sessionData: any, // createSession 的返回值
  content: string,
  options: {
    sceneId?: string;
    webSearchEnabled?: boolean;
    deepThinkEnabled?: boolean;
    messageType?: string;
    files?: Array<any>;
    knowledgeId?: string | null;
  } = {}
): ChatCompletionsParams => {
  // 从 createSession 结果中提取参数
  // sessionData 本身就是 resultObject，不需要再访问 .resultObject
  const { botId, sessionId, tenantId } = sessionData;
  
  // 生成唯一的客户端ID
  const clientId = generateUUID();
  
  // 构建消息对象
  const message: ChatMessage = {
    role: CHAT_CONFIG.MESSAGE_ROLES.USER,
    content,
    type: (options.messageType as any) || CHAT_CONFIG.MESSAGE_TYPES.USER_INPUT,
    ...(options.files && { files: options.files }),
  };
  
  // 构建完整参数
  return {
    botId: String(botId),
    sessionId: String(sessionId),
    tenantId: String(tenantId),
    message,
    clientId,
    commonFlag: CHAT_CONFIG.DEFAULT_FLAGS.commonFlag,
    sceneId: options.sceneId || CHAT_CONFIG.DEFAULT_SCENE_ID,
    webSearchEnabled: options.webSearchEnabled ?? CHAT_CONFIG.DEFAULT_FLAGS.webSearchEnabled,
    deepThinkEnabled: options.deepThinkEnabled ?? CHAT_CONFIG.DEFAULT_FLAGS.deepThinkEnabled,
    params: {
      knowledgeId: options.knowledgeId || null,
    },
  };
};

/**
 * 发送聊天消息
 * 使用统一的 apiClient，自动获得 Bearer Token 鉴权
 */
export const sendChatMessage = async (
  params: ChatCompletionsParams
): Promise<ChatCompletionsResponse> => {
  // 构建特殊的请求头（保留 BOTE 系统必需的头部）
  const headers = {
    'Tenant-Id': params.tenantId,
    'Content-Type': 'application/json',
  };

  const response = await apiClient.post<ChatCompletionsResponse>(
    `${SESSION_CONFIG.BASE_URL}/chat/completions`,
    params,
    { headers }
  );

  return response;
};

/**
 * 便捷方法：基于会话数据发送消息
 */
export const sendMessageWithSession = async (
  sessionData: any, // createSession 的返回值
  content: string,
  options: {
    sceneId?: string;
    webSearchEnabled?: boolean;
    deepThinkEnabled?: boolean;
    files?: Array<any>;
    knowledgeId?: string | null;
  } = {}
): Promise<ChatCompletionsResponse> => {
  const chatParams = buildChatParams(sessionData, content, options);
  return await sendChatMessage(chatParams);
};

// SSE 事件处理回调类型
export interface SSECallbacks {
  onopen?: () => void;
  onmessage?: (event: { type: string; data: any }) => void;
  onclose?: () => void;
  onerror?: (error: any) => void;
}

/**
 * 发送流式聊天消息 (SSE)
 * 基于 react-native-sse 实现流式响应处理
 */
export const sendStreamingChatMessage = async (
  params: ChatCompletionsParams,
  callbacks: SSECallbacks
): Promise<EventSource> => {
  // 构建 SSE 请求 URL 和参数
  const url = `${SESSION_CONFIG.BASE_URL}/chat/completions`;
  
  // 获取认证令牌
  const token = await getAuthToken();
  
  // 构建请求头
  const headers = {
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Tenant-Id': params.tenantId,
  };

  // 调试信息
  console.log('🚀 发起 SSE 请求:');
  console.log('URL:', url);
  console.log('Headers:', headers);
  console.log('Params:', JSON.stringify(params, null, 2));

  // 创建 EventSource 实例
  const eventSource = new EventSource(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(params),
  });

  // 设置事件监听器
  eventSource.addEventListener('open', () => {
    console.log('✅ SSE 连接已建立');
    callbacks.onopen?.();
  });

  eventSource.addEventListener('message', (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log('📨 收到 SSE 消息:', data);
      callbacks.onmessage?.({ type: 'message', data });
    } catch (error) {
      console.error('❌ SSE 消息解析失败:', error);
      callbacks.onerror?.(error);
    }
  });

  // 处理特定事件类型
  const eventTypes = ['text', 'done', 'error', 'contextId', 'scene'];
  eventTypes.forEach(eventType => {
    eventSource.addEventListener(eventType, (event) => {
      try {
        // 根据事件类型决定是否需要JSON解析
        let data;
        if (eventType === 'text' || eventType === 'contextId') {
          // text 和 contextId 事件数据通常是纯文本
          data = event.data;
        } else if (eventType === 'done') {
          // done 事件可能是纯文本 "DONE" 或 JSON
          try {
            data = JSON.parse(event.data);
          } catch {
            // 如果解析失败，直接使用原始数据
            data = event.data;
          }
        } else {
          // error 和 scene 事件尝试解析JSON
          data = JSON.parse(event.data);
        }
        
        console.log(`📨 收到 ${eventType} 事件:`, data);
        callbacks.onmessage?.({ type: eventType, data });
      } catch (error) {
        console.error(`❌ ${eventType} 事件解析失败:`, error);
        // 对于done事件的解析错误，不要触发onerror，只是记录日志
        if (eventType !== 'done') {
          callbacks.onerror?.(error);
        }
      }
    });
  });

  eventSource.addEventListener('error', (error) => {
    console.error('❌ SSE 连接错误:', error);
    callbacks.onerror?.(error);
  });

  eventSource.addEventListener('close', () => {
    console.log('🔚 SSE 连接已关闭');
    callbacks.onclose?.();
  });

  return eventSource;
};

// 获取认证令牌的辅助函数
const getAuthToken = async (): Promise<string> => {
  try {
    // 优先从 authStore 获取 token
    const { useAuthStore } = await import('../../stores/authStore');
    const authState = useAuthStore.getState();
    
    if (authState.token) {
      return authState.token;
    }
    
    // 如果 authStore 中没有，从 AsyncStorage 获取
    const token = await AsyncStorage.getItem('token');
    
    if (token) {
      return token;
    }
    
    console.warn('⚠️ 未找到认证令牌，使用默认令牌');
    return 'default-token';
  } catch (error) {
    console.warn('⚠️ 获取认证令牌失败，使用默认令牌:', error);
    return 'default-token';
  }
};

/**
 * 便捷方法：基于会话数据发送流式消息
 */
export const sendStreamingMessageWithSession = async (
  sessionData: any,
  content: string,
  callbacks: SSECallbacks,
  options: {
    sceneId?: string;
    webSearchEnabled?: boolean;
    deepThinkEnabled?: boolean;
    files?: Array<any>;
    knowledgeId?: string | null;
  } = {}
): Promise<EventSource> => {
  const chatParams = buildChatParams(sessionData, content, options);
  return await sendStreamingChatMessage(chatParams, callbacks);
};

