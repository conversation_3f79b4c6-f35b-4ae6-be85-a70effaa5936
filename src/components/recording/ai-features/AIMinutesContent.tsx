import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Share,
  Image,
  Clipboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Markdown from 'react-native-markdown-display';
import {
  colors,
  spacing,
  typography,
  baseMarkdownStyles,
} from '../../../styles';
import { Recording } from '@/types';
import { DialogUtil } from '@/utils/dialogUtil';

interface AIMinutesContentProps {
  recording?: Recording;
}

const AIMinutesContent: React.FC<AIMinutesContentProps> = ({ recording }) => {
  const handleShare = async () => {
    if (!recording?.meetingMinutes) return;

    try {
      await Share.share({
        url: 'https://www.hjlingxi.com/LCDP/#/userLogin',
        title: recording?.meetingMinutes,
        message: `分享Ai纪要`,
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  const handleCopy = async () => {
    if (!recording?.meetingMinutes) return;

    try {
      await Clipboard.setString(recording.meetingMinutes);
      DialogUtil.success('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const renderStatusContent = () => {
    if (
      recording?.transcriptionStatus === 'pending' ||
      !recording?.transcriptionStatus
    ) {
      return (
        <View style={styles.statusContainer}>
          <Image
            source={require('../../../../assets/images/icons/empty.png')}
            style={styles.statusImage}
            resizeMode="contain"
          />
          <Text style={styles.statusDescription}>
            暂无内容，请先完成录音转文字
          </Text>
        </View>
      );
    }

    if (recording?.transcriptionStatus === 'failed') {
      return (
        <View style={styles.statusContainer}>
          <Image
            source={require('../../../../assets/images/icons/error.png')}
            style={styles.statusImage}
            resizeMode="contain"
          />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.statusDescription}>
              小兹提醒，前方有一点点堵车，
            </Text>
            <TouchableOpacity onPress={() => DialogUtil.alert('提示', '请稍后再试')}>
              <Text style={{ color: colors.primary }}>请点击重试</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // success state - show the actual content
    return (
      <>
        <View style={styles.minutesHeader}>
          <Text style={styles.minutesTitle}>结果由人工智能合成</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity onPress={handleCopy} style={styles.actionButton}>
              <Ionicons name="copy-outline" size={22} color="#414352" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleShare} style={styles.actionButton}>
              <Ionicons name="share-outline" size={22} color="#414352" />
            </TouchableOpacity>
          </View>
        </View>
        <View>
          <Markdown style={markdownStyles}>
            {recording?.meetingMinutes || '暂无会议纪要'}
          </Markdown>
        </View>
      </>
    );
  };

  return <View style={styles.contentWrapper}>{renderStatusContent()}</View>;
};

/**
 * 会议纪要专用的Markdown样式
 *
 * 基于统一的基础样式（baseMarkdownStyles），针对会议纪要场景进行定制：
 * - 使用基础样式保证一致性
 * - 针对会议纪要内容调整标题字号（更大更突出）
 * - 调整段落间距以适应长文档阅读
 */
const markdownStyles = {
  // 继承基础样式
  ...baseMarkdownStyles,

  // 会议纪要场景的样式覆盖
  heading1: {
    ...baseMarkdownStyles.heading1,
    fontSize: 20, // 比基础样式大2px，更突出
    marginBottom: 12,
    marginTop: 16,
  },
  heading2: {
    ...baseMarkdownStyles.heading2,
    fontSize: 18, // 比基础样式大2px
    marginBottom: 10,
    marginTop: 14,
  },
  heading3: {
    ...baseMarkdownStyles.heading3,
    fontSize: 16, // 比基础样式大1px
    marginBottom: 8,
    marginTop: 12,
  },
  paragraph: {
    ...baseMarkdownStyles.paragraph,
    marginBottom: 8, // 增加段落间距，适合长文档阅读
  },
  bullet_list: {
    ...baseMarkdownStyles.bullet_list,
    marginBottom: 8, // 增加列表间距
  },
  ordered_list: {
    ...baseMarkdownStyles.ordered_list,
    marginBottom: 8, // 增加列表间距
  },
  list_item: {
    ...baseMarkdownStyles.list_item,
    marginBottom: 4, // 增加列表项间距
  },
};

const styles = StyleSheet.create({
  contentWrapper: {
    padding: 16,
  },
  minutesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginRight: 22, // 🎯 正值边距实现正确对齐
  },
  actionButton: {
    padding: 0, // 🎯 移除padding，让图标边缘精确对齐
  },
  minutesTitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },
  minutesDate: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  tips: {
    marginBottom: spacing.md,
  },
  tipsText: {
    fontSize: 14,
  },
  title: {
    marginBottom: spacing.md,
  },
  titleText: {
    fontSize: 20,
    fontWeight: typography.fontWeight.semibold,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  attendeesContainer: {
    fontSize: 16,
    paddingHorizontal: 8,
  },
  summaryText: {
    fontSize: 16,
    paddingHorizontal: 8,
  },
  decisionsContainer: {
    backgroundColor: colors.background.bg,
    borderRadius: 12,
    padding: 16,
  },
  decisionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  decisionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  decisionNumberText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  decisionText: {
    fontSize: 14,
    color: colors.text.primary,
    flex: 1,
    lineHeight: 20,
  },
  actionItemsContainer: {
    backgroundColor: colors.background.bg,
    borderRadius: 12,
    padding: 16,
  },
  actionItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  actionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  actionTask: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    flex: 1,
  },
  actionDeadline: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  actionAssignee: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  statusContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  statusImage: {
    width: 180,
    height: 180,
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AIMinutesContent;
