import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Share,
  TouchableOpacity,
  Image,
  Clipboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Markdown from 'react-native-markdown-display';
import { colors, typography, createMarkdownStyles } from '../../../styles';
import { Recording } from '@/types';
import { DialogUtil } from '@/utils/dialogUtil';

interface AISummaryContentProps {
  recording?: Recording;
}

const AISummaryContent: React.FC<AISummaryContentProps> = ({ recording }) => {
  const handleShare = async () => {
    if (!recording?.fullSummary) return;

    try {
      await Share.share({
        url: 'https://www.hjlingxi.com/LCDP/#/userLogin',
        title: recording?.fullSummary,
        message: `分享Ai速览`,
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  const handleCopy = async () => {
    if (!recording?.fullSummary) return;

    try {
      const content = `全文摘要
    ${recording?.fullSummary || ''}
章节速览
    ${recording?.chapterOverview || ''}`;
      await Clipboard.setString(content);
      DialogUtil.success('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const renderStatusContent = () => {
    if (
      recording?.transcriptionStatus === 'pending' ||
      !recording?.transcriptionStatus
    ) {
      return (
        <View style={styles.statusContainer}>
          <Image
            source={require('../../../../assets/images/icons/empty.png')}
            style={styles.statusImage}
            resizeMode="contain"
          />
          <Text style={styles.statusDescription}>
            暂无内容，请先完成录音转文字
          </Text>
        </View>
      );
    }

    if (recording?.transcriptionStatus === 'failed') {
      return (
        <View style={styles.statusContainer}>
          <Image
            source={require('../../../../assets/images/icons/error.png')}
            style={styles.statusImage}
            resizeMode="contain"
          />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.statusDescription}>
              小兹提醒，前方有一点点堵车，
            </Text>
            <TouchableOpacity onPress={() => DialogUtil.alert('提示', '请稍后再试')}>
              <Text style={{ color: colors.primary }}>请点击重试</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // success state - show the actual content
    return (
      <>
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>结果由人工智能合成</Text>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                onPress={handleCopy}
                style={styles.actionButton}
              >
                <Ionicons name="copy-outline" size={22} color="#414352" />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleShare}
                style={styles.actionButton}
              >
                <Ionicons name="share-outline" size={22} color="#414352" />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.keyPointWrapper}>
            <Ionicons
              name="document-text-outline"
              size={24}
              color={colors.text.primary}
            />
            <Text style={styles.keyPointTitle}>全文摘要</Text>
          </View>
          <View style={styles.keyPointsContainer}>
            <Text style={styles.keyPointText}>
              {recording.fullSummary || '暂无摘要'}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.keyPointWrapper}>
            <Ionicons
              name="document-text-outline"
              size={24}
              color={colors.text.primary}
            />
            <Text style={styles.keyPointTitle}>章节速览</Text>
          </View>
          <View>
            <Markdown style={markdownStyles}>
              {recording?.chapterOverview || '暂无章节速览'}
            </Markdown>
          </View>
        </View>
      </>
    );
  };

  return <View style={styles.contentWrapper}>{renderStatusContent()}</View>;
};

/**
 * 摘要内容专用的Markdown样式
 *
 * 基于统一的基础样式（baseMarkdownStyles），针对摘要场景进行定制：
 * - 使用紧凑变体（compact）减少间距
 * - 列表项使用次要颜色，突出层次感
 * - 适合信息密度较高的摘要内容展示
 */
const markdownStyles = {
  // 使用紧凑变体作为基础
  ...createMarkdownStyles({ variant: 'compact' }),

  // 摘要场景的特殊样式覆盖
  body: {
    fontSize: 14,
    color: colors.text.secondary, // 使用次要颜色
    lineHeight: 18,
    fontFamily: typography.fontFamily,
  },
  list_item: {
    fontSize: 14,
    color: colors.text.secondary, // 列表项使用次要颜色，突出层次
    lineHeight: 18,
    marginBottom: 1, // 更紧凑的间距
    fontFamily: typography.fontFamily,
  },
};

const styles = StyleSheet.create({
  contentWrapper: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginRight: 22,
  },
  actionButton: {
    padding: 0, // 🎯 移除padding，让图标边缘精确对齐
  },
  sectionTitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 12,
  },
  keyPointsContainer: {
    // backgroundColor: colors.background.bg,
    // borderRadius: 12,
    // padding: 16,
  },
  keyPointWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  keyPointTitle: {
    fontSize: 20,
    marginLeft: 8,
    fontWeight: typography.fontWeight.semibold,
  },
  keyPointItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 6,
    marginRight: 12,
  },
  keyPointText: {
    fontSize: 14,
    color: colors.text.primary,
    flex: 1,
    lineHeight: 20,
  },
  topicsContainer: {
    // backgroundColor: colors.background.bg,
    // borderRadius: 12,
    // padding: 16,
  },
  topicItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  topicName: {
    fontSize: 14,
    color: colors.text.primary,
    width: 80,
  },
  topicBar: {
    flex: 1,
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    marginHorizontal: 12,
  },
  topicFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  topicCount: {
    fontSize: 14,
    color: colors.text.secondary,
    width: 20,
    textAlign: 'right',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: colors.background.bg,
    borderRadius: 12,
    padding: 16,
    flex: 1,
    marginHorizontal: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    marginTop: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: 2,
  },
  statusContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  statusImage: {
    width: 180,
    height: 180,
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AISummaryContent;
