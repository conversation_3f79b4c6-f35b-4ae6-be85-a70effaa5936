import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Markdown from 'react-native-markdown-display';
import { ChatMessage } from '@/types';
import { baseMarkdownStyles } from '@styles';
import LoadingDots from '../LoadingDots';

interface MessageRendererProps {
  message: ChatMessage;
  streamingMessage: ChatMessage | null;
  displayedContent: string;
  isTyping: boolean;
  cursorVisible: boolean;
  receivedContent: string;
  isStreamingComplete: boolean;
  skipTypewriterAnimation: () => void;
  styles: any;
}

// Markdown 样式配置
const chatMarkdownStyles = {
  ...baseMarkdownStyles,
  body: {
    ...baseMarkdownStyles.body,
    fontSize: 14,
    lineHeight: 22, // 调整行高以改善垂直对齐
    color: '#414352',
    margin: 0, // 移除body的默认margin
  },
  paragraph: {
    ...baseMarkdownStyles.paragraph,
    fontSize: 14,
    lineHeight: 22, // 保持一致的行高
    color: '#414352',
    marginTop: 0,
    marginBottom: 0, // 移除底部margin以确保上下padding一致
  },
  text: {
    fontSize: 14,
    lineHeight: 22,
    color: '#414352',
    margin: 0,
  },
};

export const MessageRenderer: React.FC<MessageRendererProps> = ({
  message,
  streamingMessage,
  displayedContent,
  isTyping,
  cursorVisible,
  receivedContent,
  isStreamingComplete,
  skipTypewriterAnimation,
  styles,
}) => {
  // 渲染用户消息
  const renderUserMessage = (message: ChatMessage) => (
    <View key={message.id} style={styles.userMessageContainer}>
      <LinearGradient
        colors={['#3053ED', '#5279FF80', '#3EDDB6']}
        start={{ x: -0.12, y: -0.038 }}
        end={{ x: 1.024, y: 1.047 }}
        style={styles.userMessageBubble}
      >
        <Text style={styles.userMessageText}>{message.content || ''}</Text>
      </LinearGradient>
    </View>
  );

  // 渲染Bot消息
  const renderBotMessage = (message: ChatMessage) => {
    // 判断是否是正在流式传输的消息
    const isStreamingMessage =
      streamingMessage && message.id === streamingMessage.id;

    // 确定显示内容：
    // 1. 如果是流式消息，使用打字机显示的内容
    // 2. 如果是历史消息，直接使用消息内容
    // 3. 如果内容为空，使用空字符串（避免undefined导致的渲染问题）
    const displayContent = isStreamingMessage
      ? displayedContent
      : message.content || '';

    // 调试历史消息 - 只在内容为空时输出，减少日志噪音
    if (
      message.role === 'assistant' &&
      !isStreamingMessage &&
      !displayContent
    ) {
      console.warn('⚠️ 历史消息内容为空:', {
        messageId: message.id,
        messageContent: message.content,
        messageStatus: message.status,
        messageTimestamp: message.timestamp,
      });
    }

    // 判断是否需要显示加载状态
    // 只有当是流式消息且没有接收到内容且流式传输未完成时才显示加载状态
    const shouldShowLoading =
      isStreamingMessage &&
      receivedContent.length === 0 &&
      !isStreamingComplete;

    // 调试加载状态
    if (isStreamingMessage) {
      console.log('🔍 加载状态检查:', {
        messageId: message.id,
        isStreamingMessage,
        receivedContentLength: receivedContent.length,
        isStreamingComplete,
        shouldShowLoading,
        messageContent: message.content?.substring(0, 20) + '...',
      });
    }

    return (
      <View key={message.id} style={styles.botMessageContainer}>
        <View style={styles.botMessageContent}>
          <TouchableOpacity
            style={styles.botMessageBubble}
            onPress={
              isStreamingMessage && isTyping
                ? skipTypewriterAnimation
                : undefined
            }
            activeOpacity={isStreamingMessage && isTyping ? 0.7 : 1}
          >
            {shouldShowLoading ? (
              <View style={styles.loadingContainer}>
                <LoadingDots />
                <Text style={styles.thinkingText}>AI正在思考中...</Text>
              </View>
            ) : (
              <Markdown style={chatMarkdownStyles}>
                {(displayContent || '') +
                  (isStreamingMessage && isTyping && cursorVisible ? '|' : '')}
              </Markdown>
            )}
          </TouchableOpacity>
          <View style={styles.messageActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="copy-outline" size={16} color="#9092A3" />
            </TouchableOpacity>
            <View style={styles.actionSeparator} />
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="thumbs-up-outline" size={16} color="#9092A3" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="thumbs-down-outline" size={16} color="#9092A3" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  // 根据消息类型渲染不同的组件
  return message.role === 'user'
    ? renderUserMessage(message)
    : renderBotMessage(message);
};

export default MessageRenderer;
