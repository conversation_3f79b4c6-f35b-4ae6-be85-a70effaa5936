import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Image,
} from 'react-native';
import Slider from '@react-native-community/slider';
import { useAudioPlayer } from '../../hooks';
import { colors } from '../../styles';
import { useMediaPlayerContext } from '../../contexts/MediaPlayerContext';

interface MediaPlayerProps {
  audioUri: string;
  duration: number;
  onHeightChange?: (height: number) => void; // 新增：高度变化回调
}

export interface MediaPlayerRef {
  stop: () => void;
}

const MediaPlayer = forwardRef<MediaPlayerRef, MediaPlayerProps>(
  ({ audioUri, duration, onHeightChange }, ref) => {
    const { setPlayerHeight } = useMediaPlayerContext();

    const {
      playbackStatus,
      playbackSpeed,
      loadAudio,
      playPause,
      seekTo,
      changeSpeed,
      forward,
      rewind,
      stop,
    } = useAudioPlayer();

    const [showSpeedModal, setShowSpeedModal] = useState(false);

    const speedOptions = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

    useEffect(() => {
      if (audioUri) {
        loadAudio(audioUri);
      }
    }, [audioUri]);

    // 暴露停止函数给父组件
    useImperativeHandle(
      ref,
      () => ({
        stop,
      }),
      [stop]
    );

    const handleSeek = (value: number) => {
      seekTo(value * 1000);
    };

    const handleSpeedChange = (speed: number) => {
      changeSpeed(speed);
      setShowSpeedModal(false);
    };

    const rewind5Seconds = () => {
      rewind(5);
    };

    const forward5Seconds = () => {
      forward(5);
    };

    // 处理容器高度变化
    const handleLayout = (event: any) => {
      const { height } = event.nativeEvent.layout;
      setPlayerHeight(height); // 更新Context中的高度
      onHeightChange?.(height); // 保持向后兼容
    };

    const formatTime = (seconds: number) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs
        .toString()
        .padStart(2, '0')}`;
    };

    const currentTime = playbackStatus.positionMillis / 1000;
    const totalTime = playbackStatus.durationMillis / 1000; // 使用实际音频时长
    const isLoaded = playbackStatus.isLoaded;
    const isPlaying = playbackStatus.isPlaying;

    return (
      <View style={styles.container} onLayout={handleLayout}>
        {/* 进度条 */}
        <View style={styles.progressContainer}>
          <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
          <Slider
            style={styles.progressBar}
            minimumValue={0}
            maximumValue={totalTime || duration} // 优先使用实际音频时长，回退到传入的duration
            value={currentTime}
            onValueChange={handleSeek}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.border}
            thumbTintColor={colors.primary}
            disabled={!isLoaded}
          />
          <Text style={styles.timeText}>
            {formatTime(totalTime || duration)}
          </Text>
        </View>

        {/* 控制按钮 */}
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setShowSpeedModal(true)}
            disabled={!isLoaded}
          >
            <Text style={[styles.speedText, !isLoaded && styles.disabledText]}>
              {playbackSpeed}x
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={rewind5Seconds}
            disabled={!isLoaded}
          >
            <Image
              source={require('../../../assets/images/icons/icon-slow5.png')}
              resizeMode="contain"
              style={styles.controlProcess}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[!isLoaded && styles.disabledButton]}
            onPress={playPause}
            disabled={!isLoaded}
          >
            {isPlaying ? (
              <Image
                source={require('../../../assets/images/recording/pause-button.png')}
                style={styles.playButton}
                resizeMode="contain"
              />
            ) : (
              <Image
                source={require('../../../assets/images/recording/play-button.png')}
                style={styles.playButton}
                resizeMode="contain"
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={forward5Seconds}
            disabled={!isLoaded}
          >
            <Image
              source={require('../../../assets/images/icons/icon-fast5.png')}
              resizeMode="contain"
              style={styles.controlProcess}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlButton} disabled={!isLoaded}>
            <Image
              source={require('../../../assets/images/icons/icon-setting.png')}
              resizeMode="contain"
              style={styles.controlProcess}
            />
          </TouchableOpacity>
        </View>

        {/* 倍速选择弹窗 */}
        <Modal
          visible={showSpeedModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowSpeedModal(false)}
        >
          <TouchableWithoutFeedback onPress={() => setShowSpeedModal(false)}>
            <View style={styles.modalOverlay}>
              <TouchableWithoutFeedback>
                <View style={styles.speedModal}>
                  <Text style={styles.modalTitle}>播放速度</Text>
                  {speedOptions.map((speed) => (
                    <TouchableOpacity
                      key={speed}
                      style={[
                        styles.speedOption,
                        playbackSpeed === speed && styles.speedOptionActive,
                      ]}
                      onPress={() => handleSpeedChange(speed)}
                    >
                      <Text
                        style={[
                          styles.speedOptionText,
                          playbackSpeed === speed &&
                            styles.speedOptionTextActive,
                        ]}
                      >
                        {speed}x
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background.primary,
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
  },
  timeText: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  controlButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  controlProcess: {
    width: 24,
    height: 24,
  },
  controlButtonText: {
    fontSize: 10,
    color: colors.text.secondary,
    marginTop: 2,
  },
  playButton: {
    width: 32,
    height: 32,
  },
  disabledButton: {
    backgroundColor: colors.text.secondary + '40',
  },
  disabledText: {
    color: colors.text.secondary + '60',
  },
  speedText: {
    fontSize: 13,
    color: colors.text.primary,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  speedModal: {
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 300,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  speedOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  speedOptionActive: {
    backgroundColor: colors.primary + '20',
  },
  speedOptionText: {
    fontSize: 16,
    color: colors.text.primary,
  },
  speedOptionTextActive: {
    color: colors.primary,
    fontWeight: '600',
  },
  popoverOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  popover: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    backgroundColor: colors.background.primary,
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    minWidth: 200,
  },
  popoverContent: {
    flexDirection: 'column',
  },
  popoverTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 12,
  },
  popoverOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  popoverOptionText: {
    fontSize: 14,
    color: colors.text.primary,
    marginRight: 12,
  },
});

export default MediaPlayer;
