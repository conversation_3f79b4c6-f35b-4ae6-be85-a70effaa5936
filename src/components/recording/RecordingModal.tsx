import React, { useEffect, useRef, useCallback, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  StatusBar,
  Animated,
  ScrollView,
  Image,
} from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing } from '../../styles';
import Dialog from '@/components/common/Dialog';
// import { useRealtimeTranscription } from '../../hooks/useRealtimeTranscription'; // 🔧 移除，避免多个连接

interface RecordingModalProps {
  visible: boolean;
  onClose: () => void;
  onMinimize: () => void;
  onPause: () => void;
  onStop: () => void;
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  location?: string;
  // 实时转写配置
  transcriptionConfig?: {
    appId: string;
    apiKey: string;
  };
  // 接收外部传入的音频数据
  audioData?: Float32Array;
  // 🔧 接收转写状态，避免创建多个连接
  isTranscriptionConnected?: boolean;
  transcriptionText?: string;
  partialText?: string;
  // 笔记功能
  onNote?: () => void;
}

export const RecordingModal: React.FC<RecordingModalProps> = ({
  visible,
  onClose,
  onMinimize,
  onPause,
  onStop,
  isRecording,
  isPaused,
  duration,
  location,
  transcriptionConfig,
  audioData,
  // 🔧 接收转写状态，避免创建多个连接
  isTranscriptionConnected = false,
  transcriptionText: externalTranscriptionText = '',
  partialText: externalPartialText = '', // eslint-disable-line @typescript-eslint/no-unused-vars
  onNote,
}) => {
  const translateY = useRef(new Animated.Value(0)).current;
  const scrollViewRef = useRef<ScrollView>(null);
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [cancelVisible, setCancelVisible] = useState(false);

  // 🔧 使用外部传入的转写状态，避免创建多个连接
  const isConnected = isTranscriptionConnected;
  const transcriptionText = externalTranscriptionText;
  const transcriptionError = null; // 不再需要本地错误状态

  // 自动滚动到底部的功能
  const scrollToBottom = useCallback(() => {
    if (scrollViewRef.current && transcriptionText) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100); // 延迟100ms确保内容已渲染
    }
  }, [transcriptionText]);

  // 监听转文字内容变化，自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [transcriptionText, scrollToBottom]);

  // 添加调试日志
  const addDebugLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLogs((prev) => [...prev.slice(-10), `${timestamp}: ${message}`]); // 保留最近11条日志
  }, []);

  // 监控音频数据变化
  useEffect(() => {
    if (audioData && audioData.length > 0) {
      const level = Math.max(...audioData.map(Math.abs));
      const rms = Math.sqrt(
        audioData.reduce((sum, val) => sum + val * val, 0) / audioData.length
      );

      addDebugLog(
        `音频数据: 峰值=${level.toFixed(4)}, RMS=${rms.toFixed(4)}, 样本=${audioData.length}`
      );

      if (isConnected) {
        // 🔧 转写功能由RecordingScreen管理，这里只记录音频数据
        addDebugLog(`音频数据已接收，转写由RecordingScreen统一管理`);
      }
    }
  }, [audioData, isConnected, addDebugLog]); // 🔧 移除sendAudioData依赖

  // 监控连接状态变化
  useEffect(() => {
    addDebugLog(`讯飞连接状态: ${isConnected ? '已连接' : '未连接'}`);
  }, [isConnected, addDebugLog]);

  // 监控转写结果（从props接收）
  useEffect(() => {
    if (transcriptionText) {
      addDebugLog(`收到转写结果: "${transcriptionText.slice(-20)}"`);
    }
  }, [transcriptionText, addDebugLog]);

  // 监控错误
  useEffect(() => {
    if (transcriptionError) {
      addDebugLog(`转写错误: ${transcriptionError}`);
    }
  }, [transcriptionError, addDebugLog]);

  // 格式化录音时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取当前时间和日期
  const getCurrentDateTime = (): string => {
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    return `${month}-${day} ${hour}:${minute}·APP`;
  };

  // 🔧 转写状态由RecordingScreen管理，这里只显示状态
  useEffect(() => {
    if (visible && isRecording && !isPaused && transcriptionConfig) {
      addDebugLog('转写服务由RecordingScreen管理');
    }
  }, [visible, isRecording, isPaused, transcriptionConfig, addDebugLog]);

  // 🔧 清理调试日志当弹窗关闭时
  useEffect(() => {
    if (!visible) {
      setDebugLogs([]); // 清理调试日志
      addDebugLog('弹窗关闭，转写服务由RecordingScreen管理');
    }
  }, [visible, addDebugLog]);

  // 处理下滑手势
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: translateY } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = useCallback(
    (event: { nativeEvent: { state: number; translationY: number } }) => {
      if (event.nativeEvent.state === 5) {
        // GESTURE_STATE_END
        const { translationY: finalY } = event.nativeEvent;

        if (finalY > 100) {
          // 下滑超过100px，收起弹窗
          onMinimize();
        } else {
          // 回弹到原位置
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      }
    },
    [onMinimize, translateY]
  );

  // 处理遮罩点击
  const handleOverlayPress = useCallback(() => {
    onMinimize(); // 点击遮罩收起弹窗，显示胶囊
  }, [onMinimize]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      statusBarTranslucent={false}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="rgba(0,0,0,0.5)"
        translucent
      />

      {/* 遮罩区域 - 点击收起弹窗 */}
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={handleOverlayPress}
      >
        {/* 弹窗内容区域 */}
        <PanGestureHandler
          onGestureEvent={onGestureEvent}
          onHandlerStateChange={onHandlerStateChange}
          minPointers={1}
          maxPointers={1}
          shouldCancelWhenOutside={false}
        >
          <Animated.View
            style={[
              styles.container,
              {
                transform: [{ translateY }],
              },
            ]}
          >
            <View style={styles.innerContainer}>
              {/* 头部 - 可拖拽区域 */}
              <View style={styles.header}>
                {/* 拖拽指示条 */}
                <View style={styles.dragIndicator} />

                <View style={styles.headerContent}>
                  <Image
                    source={require('../../../assets/images/recording/new-recording-text.png')}
                    style={styles.titleImage}
                    resizeMode="contain"
                  />
                  <View style={styles.headerActions}>
                    <TouchableOpacity
                      style={styles.debugButton}
                      onPress={() => setShowDebugInfo(!showDebugInfo)}
                    >
                      <Ionicons
                        name={showDebugInfo ? 'bug' : 'bug-outline'}
                        size={20}
                        color={showDebugInfo ? '#FF5722' : '#666'}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.recordButton}
                      onPress={onNote}
                    >
                      <Image
                        source={require('../../../assets/images/recording/edit-icon.png')}
                        style={styles.editIcon}
                        resizeMode="contain"
                      />
                      <Text style={styles.recordButtonText}>记一下</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* 调试信息面板 */}
              {showDebugInfo && (
                <View style={styles.debugPanel}>
                  <Text style={styles.debugTitle}>调试信息</Text>
                  <ScrollView
                    style={styles.debugLogContainer}
                    showsVerticalScrollIndicator={false}
                  >
                    {debugLogs.map((log, index) => (
                      <Text key={index} style={styles.debugLogText}>
                        {log}
                      </Text>
                    ))}
                  </ScrollView>

                  <View style={styles.debugStatusRow}>
                    <Text style={styles.debugStatusLabel}>录音:</Text>
                    <Text
                      style={[
                        styles.debugStatusValue,
                        isRecording
                          ? styles.statusActive
                          : styles.statusInactive,
                      ]}
                    >
                      {isRecording ? '进行中' : '未录音'}
                    </Text>
                  </View>

                  <View style={styles.debugStatusRow}>
                    <Text style={styles.debugStatusLabel}>讯飞:</Text>
                    <Text
                      style={[
                        styles.debugStatusValue,
                        isConnected
                          ? styles.statusActive
                          : styles.statusInactive,
                      ]}
                    >
                      {isConnected ? '已连接' : '未连接'}
                    </Text>
                  </View>

                  <View style={styles.debugStatusRow}>
                    <Text style={styles.debugStatusLabel}>音频:</Text>
                    <Text style={styles.debugStatusValue}>
                      {audioData ? `${audioData.length}样本` : '无数据'}
                    </Text>
                  </View>

                  <View style={styles.debugStatusRow}>
                    <Text style={styles.debugStatusLabel}>配置:</Text>
                    <Text
                      style={[
                        styles.debugStatusValue,
                        transcriptionConfig
                          ? styles.statusActive
                          : styles.statusInactive,
                      ]}
                    >
                      {transcriptionConfig
                        ? `AppId: ${transcriptionConfig.appId}`
                        : '未配置'}
                    </Text>
                  </View>
                </View>
              )}

              {/* 时间和位置信息 */}
              <View style={styles.infoContainer}>
                <View style={styles.infoRow}>
                  <Ionicons
                    name="calendar-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.infoText}>{getCurrentDateTime()}</Text>
                </View>
                {location && (
                  <>
                    <View style={styles.infoSeparator} />
                    <View style={styles.infoRow}>
                      <Ionicons
                        name="location-outline"
                        size={16}
                        color={colors.text.secondary}
                      />
                      <Text style={styles.infoText}>{location}</Text>
                    </View>
                  </>
                )}
              </View>

              {/* 主要内容区域 - 使用flex布局充分利用空间 */}
              <View style={styles.mainContentContainer}>
                {/* 转录文本区域 */}
                <View style={styles.transcriptionContainer}>
                  {/* 转写状态指示器 */}
                  {transcriptionConfig && (
                    <View style={styles.transcriptionStatus}>
                      <View
                        style={[
                          styles.statusDot,
                          {
                            backgroundColor: isConnected
                              ? '#4CAF50'
                              : '#FF5722',
                          },
                        ]}
                      />
                      <Text style={styles.statusText}>
                        {transcriptionError
                          ? `转写错误: ${transcriptionError}`
                          : isConnected
                            ? '实时转写中...'
                            : '转写服务未连接'}
                      </Text>
                    </View>
                  )}

                  {/* 转文字滚动区域 */}
                  <ScrollView
                    ref={scrollViewRef}
                    style={styles.transcriptionScrollView}
                    contentContainerStyle={styles.transcriptionScrollContent}
                    showsVerticalScrollIndicator={true}
                    nestedScrollEnabled={true}
                  >
                    <Text style={styles.transcriptionText}>
                      {transcriptionText || ''}
                    </Text>
                  </ScrollView>
                </View>

                {/* 删除重复的录音状态显示，底部已有精美的UI设计
                <View style={styles.statusContainer}>
                  <Text style={styles.durationText}>
                    {formatDuration(duration)}
                  </Text>
                  <Text style={styles.statusText}>
                    {isPaused ? '已暂停' : isRecording ? '录音中' : '准备录音'}
                  </Text>
                </View>
                */}
              </View>
            </View>

            {/* 播放控制上方区域 - 波形、时长、录音状态 */}
            <View style={styles.playControlTopContainer}>
              <View style={styles.separatorLine} />
              <Image
                source={require('../../../assets/images/recording/waveform-icon.png')}
                style={styles.waveformIcon}
                resizeMode="contain"
              />
              <Text style={styles.recordingStatusText}>
                {formatDuration(duration)} 录音中
              </Text>
              <View style={styles.separatorLine} />
            </View>

            {/* 底部固定控制按钮 */}
            <View style={styles.bottomControlsContainer}>
              <TouchableOpacity
                onPress={() => setCancelVisible(true)}
                style={styles.sideButtonContainer}
              >
                <Image
                  source={require('../../../assets/images/recording/close-button.png')}
                  style={styles.sideButtonImage}
                  resizeMode="contain"
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={onPause}
                style={styles.pauseButtonContainer}
              >
                <Image
                  source={
                    isPaused
                      ? require('../../../assets/images/recording/play-button.png')
                      : require('../../../assets/images/recording/pause-button.png')
                  }
                  style={styles.pauseButtonImage}
                  resizeMode="contain"
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={onStop}
                style={styles.sideButtonContainer}
              >
                <Image
                  source={require('../../../assets/images/recording/check-button.png')}
                  style={styles.sideButtonImage}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </Animated.View>
        </PanGestureHandler>
      </TouchableOpacity>
      <Dialog
        visible={cancelVisible}
        title="确认取消吗？"
        onConfirm={onClose}
        onClose={() => setCancelVisible(false)}
      >
        <Text style={styles.cancelText}>
          取消后，已经录制的音频将不会保存，此操作不可撤销
        </Text>
      </Dialog>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },

  container: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '97%', // 调整为固定高度97%，让弹窗更高
    flex: 1, // 使用flex布局
    flexDirection: 'column', // 垂直布局
  },

  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.sm, // 顶部留出空间给拖拽指示条
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between', // 两端对齐
    width: '100%', // 占满整个宽度
  },

  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  dragIndicator: {
    width: 40, // 拖拽指示条的宽度
    height: 4, // 拖拽指示条的高度
    backgroundColor: '#e0e0e0', // 拖拽指示条的颜色
    borderRadius: 2, // 拖拽指示条的圆角
    alignSelf: 'center', // 水平居中
    marginBottom: spacing.sm, // 与标题之间的间距
  },

  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },

  titleImage: {
    height: 20, // 匹配设计稿中20px的字体大小
    width: 66, // 根据比例计算合适的宽度
  },

  debugButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 6,
    marginRight: spacing.sm,
  },

  recordButton: {
    backgroundColor: 'white', // 白色背景
    paddingHorizontal: 12, // 固定左右内边距
    paddingVertical: 2, // 添加少量垂直内边距，确保文字不被剪裁
    borderRadius: 16, // 使用圆角999px，在RN中使用较大值
    borderWidth: 1,
    borderColor: '#EBEBF0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // 确保内容水平居中
    gap: 4, // 图标和文字间距4px
    minHeight: 32, // 最小高度32px，允许稍微增高
  },

  editIcon: {
    width: 16,
    height: 16,
  },

  recordButtonText: {
    color: '#414352', // 使用设计稿中的文字颜色
    fontSize: 12, // 设计稿中的字号
    fontWeight: '400', // 设计稿中的字重
    lineHeight: 16, // 稍微增加行高，防止文字被剪裁
    textAlignVertical: 'center', // 确保文字垂直居中
    includeFontPadding: false, // Android特有属性，移除字体默认内边距
  },

  debugPanel: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },

  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },

  debugLogContainer: {
    maxHeight: 120,
    marginBottom: spacing.sm,
  },

  debugLogText: {
    fontSize: 11,
    color: '#495057',
    marginBottom: 2,
    fontFamily: 'monospace',
  },

  debugStatusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xs,
  },

  debugStatusLabel: {
    fontSize: 14,
    color: colors.text.secondary,
  },

  debugStatusValue: {
    fontSize: 14,
    fontWeight: '500',
  },

  statusActive: {
    color: '#28a745',
  },

  statusInactive: {
    color: '#dc3545',
  },

  infoContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    flexDirection: 'row', // 水平布局，让内容在同一行
    alignItems: 'center', // 垂直居中对齐
  },

  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // 移除 marginBottom，因为现在是水平布局
  },

  infoText: {
    fontSize: 14,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },

  infoSeparator: {
    width: 10, // 两个图标之间的间距
  },

  mainContentContainer: {
    flexDirection: 'column', // 使用垂直布局
    flex: 1, // 占据剩余所有可用空间
    paddingBottom: 120, // 为底部控制按钮留出空间
  },

  transcriptionContainer: {
    flex: 1, // 让转文字容器占据主要空间
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },

  transcriptionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },

  statusText: {
    fontSize: 12,
    color: colors.text.secondary,
  },

  transcriptionText: {
    fontSize: 16,
    color: colors.text.primary,
    lineHeight: 24,
    minHeight: 60,
  },

  transcriptionScrollView: {
    flex: 1, // 让滚动视图占据转文字容器的剩余空间
  },

  transcriptionScrollContent: {
    flexGrow: 1, // 确保内容区域可以增长
    paddingBottom: spacing.md, // 底部留一些间距
  },

  statusContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    // 移除 marginBottom 和 flex，让它保持自然高度
  },

  durationText: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },

  cancelButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#6c757d',
    justifyContent: 'center',
    alignItems: 'center',
  },

  pauseButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },

  pauseButtonActive: {
    backgroundColor: '#ffc107',
  },

  stopButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#28a745',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // 播放控制上方区域样式
  playControlTopContainer: {
    position: 'absolute',
    bottom: 120, // 在底部按钮上方
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    gap: 8, // 元素间距8px
  },

  separatorLine: {
    width: 80, // 80px宽度
    height: 0.5, // 0.5px高度
    backgroundColor: '#EBEBF0', // 分割线颜色
  },

  waveformIcon: {
    width: 16,
    height: 16,
  },

  recordingStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2A2B33',
    lineHeight: 20,
  },

  // 新的底部控制按钮样式 - 基于Figma设计稿
  bottomControlsContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center', // 改为居中对齐
    alignItems: 'center',
    paddingHorizontal: 32,
    gap: 20, // 设置按钮间的固定间距，基于设计稿调整
  },

  cancelButtonNew: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#FF5722',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  pauseButtonContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },

  pauseButtonImage: {
    width: 64,
    height: 64,
  },

  pauseButtonBackground: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#3053ED',
  },

  pauseButtonActiveNew: {
    // 暂停状态使用图片资源
  },

  sideButtonContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  sideButtonImage: {
    width: 28,
    height: 28,
  },

  stopButtonNew: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#28a745',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  innerContainer: {
    flex: 1, // 确保内容区域能够充分利用空间
  },
  cancelText: {
    color: colors.text.secondary,
    fontSize: 15,
    textAlign: 'center',
  },
});
