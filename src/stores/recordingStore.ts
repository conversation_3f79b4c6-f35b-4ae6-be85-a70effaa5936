import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Recording } from '../types';
import { lcdpService } from '@/services/api';
import Toast from 'react-native-toast-message';
import { generateStorageName } from '../utils/userIdUtils';

// 更新存储名称的函数
export const updateRecordingStorageName = async (): Promise<string> => {
  return generateStorageName('recording-storage');
};

export interface RecordingState {
  // 录音状态
  recordings: Recording[];
  currentRecording: Recording | null;
  isRecording: boolean;

  // Actions
  setRecordings: (recordings: Recording[]) => void;
  addRecording: (recording: Recording) => void;
  updateRecording: (id: string, updates: Partial<Recording>) => void;
  deleteRecording: (id: string) => void;
  batchDeleteRecordings: (ids: string[]) => void;
  setCurrentRecording: (recording: Recording | null) => void;
  setIsRecording: (isRecording: boolean) => void;
}

export const useRecordingStore = create<RecordingState>()(
  persist(
    (set, get) => ({
      // 初始状态
      recordings: [],
      currentRecording: null,
      isRecording: false,

      // Actions
      setRecordings: (recordings) => set({ recordings }),

      addRecording: async (recording) => {
        const { recordings } = get();

        try {
          // 从本地文件路径读取文件内容
          const fileInfo = await FileSystem.getInfoAsync(recording.filePath);
          if (!fileInfo.exists) {
            throw new Error('文件不存在');
          }

          // 不需要读取base64，直接使用文件路径

          // 从文件路径获取文件名和扩展名
          const fileName =
            recording.filePath.split('/').pop() || 'recording.m4a';
          const fileExtension =
            fileName.split('.').pop()?.toLowerCase() || 'm4a';

          // 根据扩展名确定MIME类型
          const mimeType =
            fileExtension === 'mp3'
              ? 'audio/mpeg'
              : fileExtension === 'wav'
                ? 'audio/wav'
                : fileExtension === 'm4a'
                  ? 'audio/mp4'
                  : 'audio/mpeg';

          // 创建React Native文件对象用于上传
          const fileObject = {
            uri: recording.filePath,
            type: mimeType,
            name: fileName,
          } as any; // React Native FormData file object

          const fileResponse = await lcdpService.uploadSingleFile(fileObject);

          if (fileResponse.resultCode !== '0') {
            Toast.show({
              type: 'error',
              text1: '文件上传失败',
              text2: fileResponse.resultMsg || '请检查网络连接',
            });
            return;
          }

          // 文件上传成功提示
          Toast.show({
            type: 'success',
            text1: '文件上传成功',
            text2: '录音文件已保存到服务器',
          });

          // 调用API上传文件
          const res = await lcdpService.addNewRecording({
            file_id: fileResponse.resultObject.fileId,
            app_file_id: recording.id,
            name: recording.title,
            duration: recording.duration,
            source: recording.type,
            location: recording.location,
          });

          if (res.resultCode === '0') {
            set({ recordings: [recording, ...recordings] });
          }
        } catch (error) {
          console.error('添加录音失败:', error);
          throw error;
        }
      },

      updateRecording: (id, updates) => {
        const { recordings } = get();
        const updatedRecordings = recordings.map((recording) =>
          recording.id === id ? { ...recording, ...updates } : recording
        );
        set({ recordings: updatedRecordings });
      },

      deleteRecording: async (id) => {
        const { recordings } = get();
        const filteredRecordings = recordings.filter(
          (recording) => recording.id !== id
        );
        const res = await lcdpService.deleteRecordingRecord({ appFileId: id });
        if (res.resultCode === '0') {
          set({ recordings: filteredRecordings });
          Toast.show({
            type: 'success',
            text1: '删除成功',
          });
        }
      },

      batchDeleteRecordings: async (ids) => {
        const { recordings } = get();
        const filteredRecordings = recordings.filter(
          (recording) => !ids.includes(recording.id)
        );
        const res = await lcdpService.batchDeleteRecordings({
          appFileIds: ids.map((id) => ({ appFileId: id })),
        });
        if (res.resultCode === '0') {
          set({ recordings: filteredRecordings });
          Toast.show({
            type: 'success',
            text1: '删除成功',
          });
        }
      },

      setCurrentRecording: (recording) => set({ currentRecording: recording }),
      setIsRecording: (isRecording) => set({ isRecording }),
    }),
    {
      name: 'recording-storage',
      storage: createJSONStorage(() => AsyncStorage),
      getStorage: () => {
        // 创建一个支持动态存储键的存储对象
        return {
          getItem: async (name) => {
            const storageName = await generateStorageName(name);
            const data = await AsyncStorage.getItem(storageName);
            return data ? JSON.parse(data) : null;
          },
          setItem: async (name, value) => {
            const storageName = await generateStorageName(name);
            await AsyncStorage.setItem(storageName, JSON.stringify(value));
          },
          removeItem: async (name) => {
            const storageName = await generateStorageName(name);
            await AsyncStorage.removeItem(storageName);
          },
        };
      },
      partialize: (state) => ({
        recordings: state.recordings.map((recording) => ({
          ...recording,
          createdAt: recording.createdAt.toISOString(),
          updatedAt: recording.updatedAt.toISOString(),
        })),
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.recordings = state.recordings.map((recording) => ({
            ...recording,
            createdAt: new Date(recording.createdAt),
            updatedAt: new Date(recording.updatedAt),
          }));
        }
      },
    }
  )
);
