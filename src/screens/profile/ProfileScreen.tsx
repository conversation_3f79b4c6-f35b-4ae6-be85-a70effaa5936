import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { typography } from '@/styles';
import { useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '@/types/navigation';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  manualMigration,
  getDetailedMigrationInfo,
  forceMigration
} from '@/utils/migrationHelper';

type NavigationProp = StackNavigationProp<RootStackParamList>;

interface SettingItem {
  id: string;
  title: string;
  icon: number; // require() 返回的是 number 类型
  onPress?: () => void;
}

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [isMigrating, setIsMigrating] = useState(false);

  // 数据迁移处理函数
  const handleDataMigration = async () => {
    try {
      setIsMigrating(true);

      // 先获取迁移状态
      const info = await getDetailedMigrationInfo();

      Alert.alert(
        '数据迁移',
        `当前状态: ${info.status}\n用户ID: ${info.details.userId}\n旧数据: ${info.details.oldDataKeys.length} 项\n新数据: ${info.details.newDataKeys.length} 项`,
        [
          {
            text: '取消',
            style: 'cancel',
          },
          {
            text: '执行迁移',
            onPress: async () => {
              const result = await manualMigration();
              Alert.alert(
                result.success ? '迁移成功' : '迁移失败',
                result.message
              );
            },
          },
          {
            text: '强制迁移',
            style: 'destructive',
            onPress: async () => {
              const result = await forceMigration();
              Alert.alert(
                result.success ? '强制迁移成功' : '强制迁移失败',
                result.message
              );
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('错误', `获取迁移状态失败: ${error}`);
    } finally {
      setIsMigrating(false);
    }
  };

  // 常用功能列表，严格按照设计稿顺序
  const functionItems: SettingItem[] = [
    {
      id: 'recharge',
      title: '会员充值',
      icon: require('../../../assets/images/settings/icon-card.png'),
      onPress: () => {
        // TODO: 实现会员充值功能
      },
    },
    {
      id: 'customer-service',
      title: '联系客服',
      icon: require('../../../assets/images/settings/icon-headphone.png'),
      onPress: () => {
        // TODO: 实现联系客服功能
      },
    },
    {
      id: 'feedback',
      title: '用户意见反馈',
      icon: require('../../../assets/images/settings/icon-feedback.png'),
      onPress: () => {
        // TODO: 实现用户意见反馈功能
      },
    },
    {
      id: 'settings',
      title: '设置',
      icon: require('../../../assets/images/settings/icon-settings.png'),
      onPress: () => {
        navigation.navigate('Settings');
      },
    },
    {
      id: 'about',
      title: '关于',
      icon: require('../../../assets/images/settings/icon-info.png'),
      onPress: () => {
        // TODO: 实现关于功能
      },
    },
  ];

  const renderFunctionItem = (item: SettingItem, index: number) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.functionItem,
        index === functionItems.length - 1 && styles.lastFunctionItem,
      ]}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.functionItemContent}>
        <View style={styles.functionIconContainer}>
          <Image source={item.icon} style={styles.functionIcon} />
        </View>
        <Text style={styles.functionTitle}>{item.title}</Text>
      </View>
      <View style={styles.functionArrow}>
        <Ionicons name="chevron-forward" size={20} color="#B0B2BF" />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* 用户信息卡片 */}
        <View style={styles.userCard}>
          {/* 背景渐变 - 使用切图 */}
          <Image
            source={require('../../../assets/images/settings/card-background-large.png')}
            style={styles.cardBackground}
            resizeMode="stretch"
          />

          {/* 用户信息 */}
          <View style={styles.userInfoSection}>
            <TouchableOpacity
              onPress={() => navigation.navigate('ProfileEdit')}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#C9E1FF', '#9FCAFF']}
                start={{ x: 0.981, y: 0.076 }}
                end={{ x: 0.028, y: 0.818 }}
                style={styles.avatarContainer}
              >
                <Image
                  source={require('../../../assets/images/settings/avatar-placeholder.png')}
                  style={styles.avatar}
                />
              </LinearGradient>
            </TouchableOpacity>
            <Text style={styles.phoneNumber}>189****3456</Text>
          </View>

          {/* VIP 信息和续费按钮 */}
          <View style={styles.vipSection}>
            <View style={styles.vipInfo}>
              <View style={styles.vipTitleRow}>
                <Image
                  source={require('../../../assets/images/settings/vip-text-gradient.png')}
                  style={styles.vipTitleImage}
                  resizeMode="contain"
                />
                <Text style={styles.vipExpiry}>2025.08.11 到期</Text>
              </View>
              <Text style={styles.vipSubtitle}>智享办公，高效进阶</Text>
            </View>
            <TouchableOpacity activeOpacity={0.8}>
              <LinearGradient
                colors={['#3053ED', '#5279FF', '#3EDDB6']}
                start={{ x: -0.12, y: -0.038 }}
                end={{ x: 1.024, y: 1.047 }}
                style={styles.renewButton}
              >
                <Text style={styles.renewButtonText}>立即续费</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* VIP 标志 */}
          <Text style={styles.vipBadge}>VIP</Text>
        </View>

        {/* 常用功能区域 */}
        <View style={styles.functionsSection}>
          <Text style={styles.sectionTitle}>常用功能</Text>
          <View style={styles.functionsContainer}>
            {functionItems.map((item, index) =>
              renderFunctionItem(item, index)
            )}
          </View>
        </View>

        {/* 临时数据迁移按钮 */}
        <View style={styles.migrationSection}>
          <TouchableOpacity
            style={styles.migrationButton}
            onPress={handleDataMigration}
            disabled={isMigrating}
            activeOpacity={0.7}
          >
            <Text style={styles.migrationButtonText}>
              {isMigrating ? '迁移中...' : '数据迁移'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F4F8FF',
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingTop: 50, // 增加顶部间距，避免与状态栏重叠
    paddingHorizontal: 16,
    paddingBottom: 100, // 为底部导航留出空间
  },

  // 用户信息卡片样式
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    padding: 16,
    position: 'relative',
    overflow: 'hidden', // 恢复隐藏，通过调整背景图片尺寸解决
    // iOS 阴影
    shadowColor: '#646778',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    // Android 阴影
    elevation: 8,
  },

  cardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '120%', // 扩展宽度确保右边不被裁切
    height: 88,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },

  userInfoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
    gap: 17,
  },

  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },

  avatar: {
    width: 60,
    height: 60,
    resizeMode: 'cover',
    marginTop: 1.5,
    marginLeft: -6,
  },

  phoneNumber: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: '600',
    color: '#2A2B33',
    lineHeight: 24,
  },

  vipSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },

  vipInfo: {
    flex: 1,
  },

  vipTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start', // 顶部对齐而不是居中
    gap: 4,
    marginBottom: 4,
  },

  vipTitleImage: {
    width: 85, // 调整以匹配设计稿
    height: 20,
  },

  vipExpiry: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '400',
    color: '#B0B2BF',
    lineHeight: 20,
    marginTop: 2, // 微调对齐
  },

  vipSubtitle: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '600',
    color: '#414352',
    lineHeight: 20,
  },

  renewButton: {
    borderRadius: 99,
    paddingHorizontal: 16,
    paddingVertical: 6,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
    height: 32,
  },

  renewButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    lineHeight: 20,
    textAlign: 'center',
  },

  vipBadge: {
    position: 'absolute',
    top: -1,
    right: 16,
    fontFamily: typography.fontFamily,
    fontSize: 56.57,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.7)',
    textShadowColor: '#FFFFFF',
    textShadowOffset: { width: -1.43, height: -1.43 },
    textShadowRadius: 0,
    height: 79,
    width: 89,
    textAlign: 'left',
    lineHeight: 79,
  },

  // 常用功能区域样式
  functionsSection: {
    flex: 1,
  },

  sectionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: '400',
    color: '#9092A3',
    lineHeight: 20,
    marginBottom: 4,
    paddingHorizontal: 16,
  },

  functionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
  },

  functionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 16,
    paddingRight: 16,
    paddingVertical: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#EBEBF0',
  },

  lastFunctionItem: {
    borderBottomWidth: 0,
  },

  functionItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  functionIconContainer: {
    width: 24,
    height: 24,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },

  functionIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },

  functionTitle: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: '400',
    color: '#414352',
    lineHeight: 24,
    flex: 1,
  },

  functionArrow: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  // 数据迁移按钮样式
  migrationSection: {
    marginTop: 20,
    paddingHorizontal: 16,
  },

  migrationButton: {
    backgroundColor: '#FF4444',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#FF4444',
  },

  migrationButtonText: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    lineHeight: 20,
  },
});

export default ProfileScreen;
