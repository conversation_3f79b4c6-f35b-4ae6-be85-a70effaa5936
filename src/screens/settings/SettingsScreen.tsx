import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@/styles';
import { useNavigation } from '@react-navigation/native';
import { useAuthStore } from '@/stores';
import Toast from 'react-native-toast-message';
import { DialogUtil } from '@/utils/dialogUtil';

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress?: () => void;
  showArrow?: boolean;
}

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { logout } = useAuthStore();

  const handleLogout = () => {
    DialogUtil.confirm(
      '退出登录',
      '确定要退出登录吗？',
      async () => {
        try {
          await logout();
          Toast.show({
            type: 'success',
            text1: '已退出登录',
          });
        } catch (error) {
          Toast.show({
            type: 'error',
            text1: '退出失败',
            text2: '请稍后重试',
          });
        }
      },
      '确定'
    );
  };

  // 账号与绑定设置
  const accountSettings: SettingItem[] = [
    {
      id: 'account-binding',
      title: '账号与绑定设置',
      icon: 'person-circle-outline',
      showArrow: true,
      onPress: () => {
        // TODO: 导航到账号绑定设置页面
        Toast.show({
          type: 'info',
          text1: '功能开发中',
        });
      },
    },
    {
      id: 'device-management',
      title: '登录设备管理',
      icon: 'phone-portrait-outline',
      showArrow: true,
      onPress: () => {
        // TODO: 导航到设备管理页面
        Toast.show({
          type: 'info',
          text1: '功能开发中',
        });
      },
    },
  ];

  // 产品信息
  const productSettings: SettingItem[] = [
    {
      id: 'privacy-policy',
      title: '个人信息收集与使用清单',
      icon: 'document-text-outline',
      showArrow: true,
      onPress: () => {
        // TODO: 导航到隐私政策页面
        Toast.show({
          type: 'info',
          text1: '功能开发中',
        });
      },
    },
    {
      id: 'third-party-sharing',
      title: '个人信息第三方共享清单',
      icon: 'share-outline',
      showArrow: true,
      onPress: () => {
        // TODO: 导航到第三方共享清单页面
        Toast.show({
          type: 'info',
          text1: '功能开发中',
        });
      },
    },
    {
      id: 'about',
      title: '关于',
      subtitle: '10.1.35',
      icon: 'information-circle-outline',
      showArrow: true,
      onPress: () => {
        // TODO: 导航到关于页面
        Toast.show({
          type: 'info',
          text1: '功能开发中',
        });
      },
    },
  ];

  const renderSettingItem = (item: SettingItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.settingItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingItemLeft}>
        <Ionicons name={item.icon} size={24} color={colors.text.primary} />
        <View style={styles.settingItemText}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          {item.subtitle && (
            <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
          )}
        </View>
      </View>
      {item.showArrow && (
        <Ionicons name="chevron-forward" size={20} color={colors.text.secondary} />
      )}
    </TouchableOpacity>
  );

  const renderSection = (title: string, items: SettingItem[]) => (
    <View style={styles.section}>
      {title && (
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{title}</Text>
        </View>
      )}
      <View style={styles.sectionContent}>
        {items.map((item, index) => (
          <View key={item.id}>
            {renderSettingItem(item)}
            {index < items.length - 1 && <View style={styles.separator} />}
          </View>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* 账号与绑定设置 */}
        {renderSection('', accountSettings)}

        {/* 产品信息 */}
        {renderSection('产品信息', productSettings)}

        {/* 退出登录 */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.settingItem, styles.logoutItem]}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <Text style={styles.logoutText}>退出登录</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F8FA',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '400',
    color: '#9092A3',
    fontFamily: typography.fontFamily,
  },
  sectionContent: {
    backgroundColor: colors.background.primary,
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 15,
    fontWeight: '400',
    color: '#414352',
    fontFamily: typography.fontFamily,
  },
  settingSubtitle: {
    fontSize: 13,
    color: colors.text.secondary,
    marginTop: 2,
    fontFamily: typography.fontFamily,
  },
  separator: {
    height: 0.5,
    backgroundColor: '#EBEBF0',
    marginLeft: 56, // 对齐文字内容
  },
  logoutItem: {
    justifyContent: 'center',
    backgroundColor: colors.background.primary,
    borderRadius: 16,
  },
  logoutText: {
    fontSize: 15,
    fontWeight: '400',
    color: '#FF615C',
    textAlign: 'center',
    fontFamily: typography.fontFamily,
  },
});

export default SettingsScreen;
