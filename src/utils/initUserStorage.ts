import { updateKnowledgeStorageName } from '@/stores/knowledgeStore';
import { updateRecordingStorageName } from '@/stores/recordingStore';
import { getUserId } from './userIdUtils';
import { checkMigrationNeeded, performDataMigration } from './dataMigration';

/**
 * 初始化用户存储
 * 当用户登录或应用启动时调用此函数，以确保所有存储使用正确的用户ID
 */
export const initUserStorage = async (): Promise<void> => {
  try {
    // 获取当前用户ID
    const userId = await getUserId();

    // 确保用户ID被记录在日志中，方便调试
    console.log('初始化用户存储: 用户ID =', userId);

    // 检查是否需要数据迁移
    const needsMigration = await checkMigrationNeeded();
    if (needsMigration) {
      console.log('检测到需要数据迁移，开始执行迁移...');
      await performDataMigration();
      console.log('数据迁移完成');
    } else {
      console.log('无需数据迁移');
    }

    // 更新知识库存储名称（这将使用新的基于 getUserId 的方式）
    await updateKnowledgeStorageName();

    // 更新录音存储名称
    await updateRecordingStorageName();

    // 清理缓存或执行其他需要的初始化操作
    console.log('用户存储初始化成功');
  } catch (error) {
    console.error('初始化用户存储失败:', error);
  }
};
