import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  manualMigration, 
  getDetailedMigrationInfo, 
  listAllStorageKeys,
  clearAllStorageData,
  forceMigration 
} from './migrationHelper';

/**
 * 测试数据迁移功能
 * 这个文件用于测试和调试数据迁移
 */

/**
 * 创建测试用的旧格式数据
 */
export const createTestOldData = async (): Promise<void> => {
  try {
    // 创建测试录音数据
    const testRecordingData = {
      state: {
        recordings: [
          {
            id: 'test-recording-1',
            title: '测试录音1',
            duration: 30000,
            filePath: '/test/path/recording1.m4a',
            type: 'voice_memo',
            location: '测试位置',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'test-recording-2',
            title: '测试录音2',
            duration: 45000,
            filePath: '/test/path/recording2.m4a',
            type: 'meeting',
            location: '会议室',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
        currentRecording: null,
        isRecording: false,
      },
      version: 0,
    };

    // 创建测试知识库数据
    const testKnowledgeData = {
      state: {
        documents: [
          {
            id: 'test-doc-1',
            title: '测试文档1',
            content: '这是测试文档的内容',
            category: 'general',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
        categories: [
          {
            id: 'general',
            name: '通用',
            color: '#007AFF',
          },
        ],
      },
      version: 0,
    };

    // 保存到旧格式的键名
    await AsyncStorage.setItem('recording-storage', JSON.stringify(testRecordingData));
    await AsyncStorage.setItem('knowledge-storage', JSON.stringify(testKnowledgeData));

    console.log('测试数据创建成功');
  } catch (error) {
    console.error('创建测试数据失败:', error);
  }
};

/**
 * 运行完整的迁移测试
 */
export const runMigrationTest = async (): Promise<void> => {
  try {
    console.log('=== 开始迁移测试 ===');

    // 1. 清理所有数据
    console.log('1. 清理现有数据...');
    await clearAllStorageData();

    // 2. 创建测试数据
    console.log('2. 创建测试数据...');
    await createTestOldData();

    // 3. 检查初始状态
    console.log('3. 检查初始状态...');
    const initialInfo = await getDetailedMigrationInfo();
    console.log('初始状态:', initialInfo);

    // 4. 执行迁移
    console.log('4. 执行迁移...');
    const migrationResult = await manualMigration();
    console.log('迁移结果:', migrationResult);

    // 5. 检查迁移后状态
    console.log('5. 检查迁移后状态...');
    const finalInfo = await getDetailedMigrationInfo();
    console.log('最终状态:', finalInfo);

    // 6. 列出所有存储键
    console.log('6. 列出所有存储键...');
    const keys = await listAllStorageKeys();
    console.log('存储键:', keys);

    console.log('=== 迁移测试完成 ===');
  } catch (error) {
    console.error('迁移测试失败:', error);
  }
};

/**
 * 验证迁移后的数据完整性
 */
export const verifyMigrationData = async (): Promise<{
  success: boolean;
  details: string[];
}> => {
  try {
    const details: string[] = [];
    let success = true;

    // 获取迁移信息
    const info = await getDetailedMigrationInfo();
    details.push(`迁移状态: ${info.status}`);
    details.push(`用户ID: ${info.details.userId}`);

    // 检查新数据是否存在
    for (const key of info.details.newDataKeys) {
      const data = await AsyncStorage.getItem(key);
      if (data) {
        const parsedData = JSON.parse(data);
        details.push(`✓ ${key}: 包含 ${JSON.stringify(parsedData).length} 字符的数据`);
      } else {
        details.push(`✗ ${key}: 数据不存在`);
        success = false;
      }
    }

    // 检查旧数据是否还存在
    for (const key of info.details.oldDataKeys) {
      details.push(`⚠ 旧数据仍存在: ${key}`);
    }

    return { success, details };
  } catch (error) {
    return {
      success: false,
      details: [`验证失败: ${error instanceof Error ? error.message : '未知错误'}`],
    };
  }
};

/**
 * 打印迁移状态报告
 */
export const printMigrationReport = async (): Promise<void> => {
  try {
    console.log('\n=== 数据迁移状态报告 ===');
    
    const info = await getDetailedMigrationInfo();
    console.log(`状态: ${info.status}`);
    console.log(`用户ID: ${info.details.userId}`);
    console.log(`迁移完成: ${info.details.migrationCompleted ? '是' : '否'}`);
    console.log(`存在旧数据: ${info.details.oldDataExists ? '是' : '否'}`);
    
    if (info.details.oldDataKeys.length > 0) {
      console.log('旧数据键:');
      info.details.oldDataKeys.forEach(key => console.log(`  - ${key}`));
    }
    
    if (info.details.newDataKeys.length > 0) {
      console.log('新数据键:');
      info.details.newDataKeys.forEach(key => console.log(`  - ${key}`));
    }

    const verification = await verifyMigrationData();
    console.log('\n数据验证:');
    verification.details.forEach(detail => console.log(`  ${detail}`));
    
    console.log(`\n整体状态: ${verification.success ? '✓ 正常' : '✗ 异常'}`);
    console.log('========================\n');
  } catch (error) {
    console.error('生成报告失败:', error);
  }
};
