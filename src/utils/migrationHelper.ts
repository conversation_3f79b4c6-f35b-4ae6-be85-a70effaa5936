import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  checkMigrationNeeded, 
  performDataMigration, 
  resetMigrationFlag, 
  getMigrationStatus 
} from './dataMigration';

/**
 * 迁移助手工具
 * 提供手动迁移和调试功能
 */

/**
 * 手动执行数据迁移
 * 用于在设置页面或调试时手动触发迁移
 */
export const manualMigration = async (): Promise<{
  success: boolean;
  message: string;
}> => {
  try {
    const status = await getMigrationStatus();
    
    if (status.migrationCompleted && !status.oldDataExists) {
      return {
        success: true,
        message: '数据已经迁移完成，无需重复迁移',
      };
    }

    if (!status.oldDataExists) {
      return {
        success: true,
        message: '没有发现需要迁移的旧数据',
      };
    }

    await performDataMigration();
    
    return {
      success: true,
      message: '数据迁移成功完成',
    };
  } catch (error) {
    console.error('手动迁移失败:', error);
    return {
      success: false,
      message: `迁移失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
};

/**
 * 强制重新迁移
 * 重置迁移标记并重新执行迁移
 */
export const forceMigration = async (): Promise<{
  success: boolean;
  message: string;
}> => {
  try {
    await resetMigrationFlag();
    const result = await manualMigration();
    
    if (result.success) {
      return {
        success: true,
        message: '强制重新迁移成功完成',
      };
    } else {
      return result;
    }
  } catch (error) {
    console.error('强制迁移失败:', error);
    return {
      success: false,
      message: `强制迁移失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
};

/**
 * 获取详细的迁移状态信息
 */
export const getDetailedMigrationInfo = async (): Promise<{
  status: string;
  details: {
    migrationCompleted: boolean;
    oldDataExists: boolean;
    userId: number;
    oldDataKeys: string[];
    newDataKeys: string[];
  };
}> => {
  try {
    const status = await getMigrationStatus();
    
    // 检查具体的旧数据键
    const oldDataKeys: string[] = [];
    const possibleOldKeys = ['recording-storage', 'knowledge-storage'];
    
    for (const key of possibleOldKeys) {
      const data = await AsyncStorage.getItem(key);
      if (data) {
        oldDataKeys.push(key);
      }
    }

    // 检查新数据键
    const newDataKeys: string[] = [];
    const possibleNewKeys = [
      `recording-storage-user-${status.userId}`,
      `knowledge-storage-user-${status.userId}`,
    ];
    
    for (const key of possibleNewKeys) {
      const data = await AsyncStorage.getItem(key);
      if (data) {
        newDataKeys.push(key);
      }
    }

    let statusText = '';
    if (status.migrationCompleted) {
      statusText = '迁移已完成';
    } else if (status.oldDataExists) {
      statusText = '需要迁移';
    } else {
      statusText = '无需迁移';
    }

    return {
      status: statusText,
      details: {
        ...status,
        oldDataKeys,
        newDataKeys,
      },
    };
  } catch (error) {
    console.error('获取迁移信息失败:', error);
    return {
      status: '获取状态失败',
      details: {
        migrationCompleted: false,
        oldDataExists: false,
        userId: 1,
        oldDataKeys: [],
        newDataKeys: [],
      },
    };
  }
};

/**
 * 清理所有存储数据（危险操作，仅用于调试）
 */
export const clearAllStorageData = async (): Promise<{
  success: boolean;
  message: string;
}> => {
  try {
    const allKeys = await AsyncStorage.getAllKeys();
    const storageKeys = allKeys.filter(key => 
      key.includes('recording-storage') || 
      key.includes('knowledge-storage') ||
      key === 'data-migration-completed'
    );

    await AsyncStorage.multiRemove(storageKeys);
    
    return {
      success: true,
      message: `已清理 ${storageKeys.length} 个存储项: ${storageKeys.join(', ')}`,
    };
  } catch (error) {
    console.error('清理存储数据失败:', error);
    return {
      success: false,
      message: `清理失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
};

/**
 * 列出所有相关的存储键
 */
export const listAllStorageKeys = async (): Promise<{
  allKeys: string[];
  storageKeys: string[];
  otherKeys: string[];
}> => {
  try {
    const allKeys = await AsyncStorage.getAllKeys();
    const storageKeys = allKeys.filter(key => 
      key.includes('recording-storage') || 
      key.includes('knowledge-storage') ||
      key === 'data-migration-completed'
    );
    const otherKeys = allKeys.filter(key => !storageKeys.includes(key));

    return {
      allKeys,
      storageKeys,
      otherKeys,
    };
  } catch (error) {
    console.error('列出存储键失败:', error);
    return {
      allKeys: [],
      storageKeys: [],
      otherKeys: [],
    };
  }
};
