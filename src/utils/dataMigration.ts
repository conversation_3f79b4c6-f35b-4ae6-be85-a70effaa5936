import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserId, generateStorageName } from './userIdUtils';

/**
 * 数据迁移工具
 * 用于将旧版本的存储数据迁移到新的用户隔离存储格式
 */

interface MigrationConfig {
  oldKey: string;
  newKeyBase: string;
  description: string;
}

const MIGRATION_CONFIGS: MigrationConfig[] = [
  {
    oldKey: 'recording-storage',
    newKeyBase: 'recording-storage',
    description: '录音数据',
  },
  {
    oldKey: 'knowledge-storage',
    newKeyBase: 'knowledge-storage',
    description: '知识库数据',
  },
];

/**
 * 检查是否需要数据迁移
 */
export const checkMigrationNeeded = async (): Promise<boolean> => {
  try {
    // 检查是否已经执行过迁移
    const migrationFlag = await AsyncStorage.getItem('data-migration-completed');
    if (migrationFlag === 'true') {
      return false;
    }

    // 检查是否存在旧格式的数据
    for (const config of MIGRATION_CONFIGS) {
      const oldData = await AsyncStorage.getItem(config.oldKey);
      if (oldData) {
        console.log(`发现需要迁移的${config.description}数据`);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('检查迁移需求时出错:', error);
    return false;
  }
};

/**
 * 执行单个存储的数据迁移
 */
const migrateSingleStorage = async (config: MigrationConfig): Promise<boolean> => {
  try {
    // 读取旧数据
    const oldData = await AsyncStorage.getItem(config.oldKey);
    if (!oldData) {
      console.log(`${config.description}: 无旧数据需要迁移`);
      return true;
    }

    // 生成新的存储键名
    const newKey = await generateStorageName(config.newKeyBase);
    
    // 检查新键是否已有数据
    const existingNewData = await AsyncStorage.getItem(newKey);
    if (existingNewData) {
      console.log(`${config.description}: 新格式数据已存在，跳过迁移`);
      return true;
    }

    // 将数据迁移到新键
    await AsyncStorage.setItem(newKey, oldData);
    console.log(`${config.description}: 数据迁移成功 ${config.oldKey} -> ${newKey}`);

    return true;
  } catch (error) {
    console.error(`迁移${config.description}数据时出错:`, error);
    return false;
  }
};

/**
 * 清理旧数据
 */
const cleanupOldData = async (): Promise<void> => {
  try {
    for (const config of MIGRATION_CONFIGS) {
      await AsyncStorage.removeItem(config.oldKey);
      console.log(`已清理旧数据: ${config.oldKey}`);
    }
  } catch (error) {
    console.error('清理旧数据时出错:', error);
  }
};

/**
 * 执行完整的数据迁移
 */
export const performDataMigration = async (): Promise<void> => {
  try {
    console.log('开始执行数据迁移...');
    
    const userId = await getUserId();
    console.log(`当前用户ID: ${userId}`);

    let allMigrationsSuccessful = true;

    // 逐个迁移每种类型的数据
    for (const config of MIGRATION_CONFIGS) {
      const success = await migrateSingleStorage(config);
      if (!success) {
        allMigrationsSuccessful = false;
      }
    }

    if (allMigrationsSuccessful) {
      // 标记迁移完成
      await AsyncStorage.setItem('data-migration-completed', 'true');
      console.log('数据迁移完成，设置迁移标记');

      // 清理旧数据（可选，为了安全起见可以保留一段时间）
      // await cleanupOldData();
      
      console.log('数据迁移成功完成！');
    } else {
      console.error('部分数据迁移失败，请检查日志');
    }
  } catch (error) {
    console.error('数据迁移过程中出错:', error);
    throw error;
  }
};

/**
 * 重置迁移状态（用于测试或重新迁移）
 */
export const resetMigrationFlag = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem('data-migration-completed');
    console.log('迁移标记已重置');
  } catch (error) {
    console.error('重置迁移标记时出错:', error);
  }
};

/**
 * 获取迁移状态信息
 */
export const getMigrationStatus = async (): Promise<{
  migrationCompleted: boolean;
  oldDataExists: boolean;
  userId: number;
}> => {
  try {
    const migrationCompleted = (await AsyncStorage.getItem('data-migration-completed')) === 'true';
    const userId = await getUserId();
    
    let oldDataExists = false;
    for (const config of MIGRATION_CONFIGS) {
      const oldData = await AsyncStorage.getItem(config.oldKey);
      if (oldData) {
        oldDataExists = true;
        break;
      }
    }

    return {
      migrationCompleted,
      oldDataExists,
      userId,
    };
  } catch (error) {
    console.error('获取迁移状态时出错:', error);
    return {
      migrationCompleted: false,
      oldDataExists: false,
      userId: 1,
    };
  }
};
