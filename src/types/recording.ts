// 🎙️ 语音识别结果的数据结构
export interface TranscriptionSegment {
  begin: string; // 开始时间(秒)
  end: string; // 结束时间(秒)
  beginTime: string; // 格式化开始时间
  endTime: string; // 格式化结束时间
  speaker: string; // 说话人ID
  language: string; // 语言类型
  text: string; // 识别文本
  text2?: string; // 翻译文本
  speakerName?: string; // 说话人名称
}

export interface NoteData {
  markTime: string;
  note: string;
}

export interface Recording {
  id: string;
  title: string;
  filePath: string;
  duration: number;
  size: number;
  transcription?: string;
  summary?: string;
  aiAnalysis?: {
    keyPoints: string[];
    actionItems: string[];
    participants: string[];
    sentiment: 'positive' | 'neutral' | 'negative';
  };
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  type?: 'import' | 'recording';
  // 转录状态
  transcriptionStatus: 'pending' | 'processing' | 'completed' | 'failed';
  transcriptionProgress?: number; // 转录进度 0-100
  location?: string; // 录音位置信息
  knowledgeId?: string; // 关联的知识库ID

  // 🆕 转录详细数据
  transcriptionData?: TranscriptionSegment[]; // 完整的识别结果数组
  transcriptionDuration?: number; // 转录处理时长(秒)
  transcriptionTimestamp?: Date; // 转录完成时间
  chapterOverview?: string; // 章节速览
  fullSummary?: string; // 全文摘要
  meetingMinutes?: string; // AI纪要

  noteData?: NoteData[];
  
  // 🆕 会话相关字段
  sessionId?: number; // 关联的会话ID
  sessionData?: Record<string, unknown>; // 会话详细数据
  chatHistory?: ChatMessage[]; // 聊天历史记录
  lastClientId?: string; // 最后一次请求的客户端ID
}

export interface RecordingState {
  isRecording: boolean;
  duration: number;
  isPaused: boolean;
  currentFile?: string;
}

export interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  playbackRate: number;
}

// 🆕 聊天消息类型
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  type: 'input' | 'point' | 'text';
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
  clientId?: string;
  files?: Array<{
    fileId: string;
    fileName: string;
    fileType: string;
  }>;
}
